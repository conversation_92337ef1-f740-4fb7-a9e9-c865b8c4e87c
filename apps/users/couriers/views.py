"""
The courier views
"""
from rest_framework.viewsets import ModelViewSet
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.conf import settings
import hashlib
import hmac
from urllib.parse import parse_qsl
import json

from django.contrib.auth import get_user_model

from apps.users.models import Courier, UserTypes
from apps.users.couriers.serializers import CourierSerializer, CourierVendorSerializer
from apps.orders.models.order import Order, OrderStatus
from apps.orders.serializers.order import OrderSerializer
from apps.orders.serializers.status import OrderStatusChangeSerializer


User = get_user_model()


class TelegramAuthView(APIView):
    """
    View for authenticating Telegram Mini App users
    """
    permission_classes = []  # Allow unauthenticated access
    authentication_classes = []  # Disable authentication

    def post(self, request):
        """
        Authenticate using Telegram's initData and get courier info
        """
        init_data = request.data.get('initData')
        if not init_data:
            return Response(
                {"detail": "initData is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            parsed_data = dict(parse_qsl(init_data))

            received_hash = parsed_data.pop('hash', '')
            if not received_hash:
                return Response(
                    {"detail": "Invalid initData: hash is missing"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            data_check_string = '\n'.join(
                f"{k}={v}" for k, v in sorted(parsed_data.items())
            )

            bot_token = settings.COURIER_BOT_TOKEN

            secret_key = hmac.new(
                "WebAppData".encode(),
                bot_token.encode(),
                hashlib.sha256
            ).digest()

            calculated_hash = hmac.new(
                secret_key,
                data_check_string.encode(),
                hashlib.sha256
            ).hexdigest()

            if calculated_hash != received_hash:
                return Response(
                    {
                        "detail": "Invalid initData: hash verification failed"
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

            user_json = parsed_data.get('user', '{}')
            if not user_json:
                return Response(
                    {"detail": "Invalid initData: user data is missing"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                user_data = json.loads(user_json)
            except json.JSONDecodeError:
                return Response(
                    {"detail": "Invalid user data format"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            telegram_id = user_data.get('id')
            if not telegram_id:
                return Response(
                    {"detail": "Telegram user ID is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                user = User.objects.get(chat_id=str(telegram_id), user_type=UserTypes.COURIER)
                refresh = RefreshToken.for_user(user)

                return Response({
                    "access": str(refresh.access_token),
                    "refresh": str(refresh)
                }, status=status.HTTP_200_OK)

            except User.DoesNotExist:
                return Response(
                    {"detail": "User with this Telegram ID not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
            except Courier.DoesNotExist:
                return Response(
                    {"detail": "Courier not found for this user"},
                    status=status.HTTP_404_NOT_FOUND
                )

        except Exception as e:
            return Response(
                {"detail": f"Authentication failed: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

class CourierViewSet(ModelViewSet):
    """
    The courier viewset for CRUD operations
    """
    serializer_class = CourierSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if hasattr(user, 'vendor'):
            # If user is a vendor, return only couriers belonging to this vendor
            return Courier.objects.filter(vendors=user.vendor)
        # Otherwise, return all couriers (e.g., admin users)
        return Courier.objects.all()

    def perform_create(self, serializer):
        # Set vendor to current user's vendor if user is vendor
        vendor = getattr(self.request.user, 'vendor', None)
        if vendor:
            serializer.save(vendors=[vendor])
        else:
            serializer.save()

    def perform_update(self, serializer):
        phone_number = self.request.data.get('phone_number')

        if phone_number:
            user = serializer.instance.user
            if user.phone_number != phone_number:
                user.phone_number = phone_number
                user.save()

        # Set vendor to current user's vendor if user is vendor
        vendor = getattr(self.request.user, 'vendor', None)
        if vendor:
            serializer.save(vendors=[vendor])
        else:
            serializer.save()

    def perform_destroy(self, instance):
        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=["get"], url_path="orders")
    def orders(self, request, pk=None):
        """
        Get active assigned orders for the courier.
        """
        try:
            courier = self.get_object()
        except Courier.DoesNotExist:
            return Response(
                {"detail": "Courier not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        user = request.user
        # Permission check: only courier themselves, vendors, and admins can access
        if user.user_type == UserTypes.COURIER:
            # Allow courier to view their own orders
            if user.courier.id == courier.id:
                pass
            else:
                return Response(
                    {"detail": "You do not have permission to view these orders."},
                    status=status.HTTP_403_FORBIDDEN
                )
        elif user.user_type == UserTypes.VENDOR:
            # Vendors can access
            pass
        elif user.user_type == UserTypes.ADMIN:
            # Admins can access
            pass
        else:
            return Response(
                {"detail": "You do not have permission to view these orders."},
                status=status.HTTP_403_FORBIDDEN
            )

        active_orders = Order.objects.filter(
            courier=courier,
            status__in=[
                OrderStatus.PENDING,
                OrderStatus.ACCEPTED,
                OrderStatus.DELIVERING,
                OrderStatus.REJECTED,
                OrderStatus.CANCELLED
            ]
        ).exclude(status=OrderStatus.FINISHED)

        serializer = OrderSerializer(active_orders, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=False, methods=["get"], url_path="by-chat-id")
    def by_chat_id(self, request):
        """
        Get courier details by chat_id
        """
        chat_id = request.query_params.get('chat_id')
        if not chat_id:
            return Response(
                {"detail": "chat_id parameter is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            user = User.objects.get(chat_id=chat_id, user_type=UserTypes.COURIER)
            courier = Courier.objects.get(user=user)
            serializer = self.get_serializer(courier)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            return Response(
                {"detail": "User with this chat_id not found."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Courier.DoesNotExist:
            return Response(
                {"detail": "Courier not found for this user."},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=["patch"], url_path="change-order-status")
    def change_order_status(self, request):
        """
        Change order status using courier's chat_id and order_id.
        """
        chat_id = request.query_params.get('chat_id')
        order_id = request.query_params.get('order_id')

        if not chat_id:
            return Response(
                {"detail": "chat_id parameter is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not order_id:
            return Response(
                {"detail": "order_id parameter is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            user = User.objects.get(chat_id=chat_id, user_type=UserTypes.COURIER)
            courier = Courier.objects.get(user=user)

            # Get the order using order_id from query params
            try:
                order = Order.objects.get(id=order_id)
            except Order.DoesNotExist:
                return Response(
                    {"detail": "Order not found."},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Check if courier is assigned to this order
            if order.courier != courier:
                if order.courier is None and order.status == OrderStatus.ACCEPTED:
                    # Courier can pick up this order
                    pass
                else:
                    return Response(
                        {"detail": "You are not assigned to this order."},
                        status=status.HTTP_403_FORBIDDEN
                    )

            # Validate and process status change
            serializer = OrderStatusChangeSerializer(
                data=request.data,
                context={'request': request, 'order': order}
            )

            if serializer.is_valid():
                try:
                    new_status = serializer.validated_data['status']

                    # Prevent changing the status from DONE to DONE
                    if order.status == OrderStatus.FINISHED and new_status == OrderStatus.FINISHED:
                        return Response(
                            {
                                "status": "error",
                                "message": "Cannot change order status from done to done"
                            },
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    delivered_quantity = serializer.validated_data.get('delivered_quantity')
                    returned_bottles = serializer.validated_data.get('returned_bottles')

                    # Update order status and quantities if status is DONE
                    if new_status == OrderStatus.FINISHED:
                        order.set_status_finished(
                            delivered_quantity=delivered_quantity,
                            returned_bottles=returned_bottles
                        )
                    else:
                        # Handle courier assignment when picking up unassigned orders
                        if new_status == OrderStatus.DELIVERING and order.courier is None:
                            order.courier = courier

                        # Update order status for other statuses
                        order.status = new_status
                        order.save()

                    status_mappings = {
                        OrderStatus.ACCEPTED: "accepted",
                        OrderStatus.REJECTED: "rejected",
                        OrderStatus.DELIVERING: "delivering",
                        OrderStatus.FINISHED: "completed",
                        OrderStatus.CANCELLED: "cancelled"
                    }

                    message = f"Order {status_mappings.get(new_status, new_status)}"
                    return Response(
                        {"status": "success", "message": message},
                        status=status.HTTP_200_OK
                    )
                except Exception as e:
                    return Response(
                        {
                            "status": "error",
                            "message": str(e)
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except User.DoesNotExist:
            return Response(
                {"detail": "User with this chat_id not found."},
                status=status.HTTP_404_NOT_FOUND
            )
        except Courier.DoesNotExist:
            return Response(
                {"detail": "Courier not found for this user."},
                status=status.HTTP_404_NOT_FOUND
            )


class CourierVendorsView(APIView):
    """
    API view to get vendors assigned to the authenticated courier
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get vendors assigned to the authenticated courier
        """
        user = request.user

        # Check if user is a courier
        if user.user_type != UserTypes.COURIER:
            return Response(
                {"detail": "Only couriers can access this endpoint."},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            courier = user.courier
        except Courier.DoesNotExist:
            return Response(
                {"detail": "Courier profile not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get vendors assigned to this courier
        vendors = courier.vendors.all()
        serializer = CourierVendorSerializer(vendors, many=True)

        return Response({
            "vendors": serializer.data
        }, status=status.HTTP_200_OK)
