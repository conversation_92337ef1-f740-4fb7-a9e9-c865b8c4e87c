"""
The courier serializer
"""
from rest_framework import serializers
from django.db import transaction
from apps.users.models import Courier, Vendor, User, UserTypes
from apps.users.common.validators import is_valid_uzb_phone_number


class CourierSerializer(serializers.ModelSerializer):
    """
    Serializer for Courier model with enhanced error handling and transactional operations
    """
    phone_number = serializers.CharField(
        source='user.phone_number',
        required=True,
        validators=[is_valid_uzb_phone_number],
        help_text="Uzbekistan format phone number"
    )
    chat_id = serializers.IntegerField(
        source='user.chat_id',
        required=False,
        allow_null=True,
        help_text="Telegram chat ID for bot communication"
    )
    password = serializers.CharField(
        write_only=True,
        required=False,
        min_length=8,
        style={'input_type': 'password'},
        help_text="Minimum 8 characters"
    )
    vendors = serializers.PrimaryKeyRelatedField(
        queryset=Vendor.objects.all(),
        many=True,
        required=False,
        help_text="Associated vendor IDs"
    )

    class Meta:
        """
        the meta class
        """
        model = Courier
        fields = [
            'id',
            'phone_number',
            'chat_id',
            'password',
            'fullname',
            'address',
            'vendors',
            'created_at',
        ]
        read_only_fields = ['id', 'created_at']

    def validate_phone_number(self, value):
        """
        Validate that phone number isn't already used by another courier
        """
        request = self.context.get('request')
        # Skip this check for updates if phone didn't change
        if request and request.method in ['PUT', 'PATCH']:
            instance = getattr(self, 'instance', None)
            if instance and instance.user and instance.user.phone_number == value:
                return value

        # Only check if phone number is already used by another courier
        # Allow the same phone number to be used for different user types (customer, vendor, etc.)
        user = User.objects.filter(phone_number=value, user_type=UserTypes.COURIER).first()
        if user:
            raise serializers.ValidationError(
                "Phone number already registered as Courier"
            )

        return value

    def to_representation(self, instance):
        """
        Include the phone number and chat_id from the associated user in the response
        """
        representation = super().to_representation(instance)
        if hasattr(instance, 'user') and instance.user:
            representation['phone_number'] = instance.user.phone_number
            representation['chat_id'] = instance.user.chat_id
            representation['user_id'] = instance.user.id
        else:
            representation['phone_number'] = None
            representation['chat_id'] = None
            representation['user_id'] = None

        # Include vendor names if vendors exist
        if instance.vendors.exists():
            representation['vendor_names'] = [vendor.name for vendor in instance.vendors.all()]

        return representation

    @transaction.atomic
    def create(self, validated_data):
        """
        Create courier with associated user in a transaction
        """
        vendors = validated_data.pop('vendors', [])
        user_data = validated_data.pop('user', {})
        phone_number = user_data.get('phone_number')
        chat_id = user_data.get('chat_id')
        password = validated_data.pop('password', None)

        if not phone_number:
            raise serializers.ValidationError("Phone number is required")

        # Check if courier user with this phone number already exists
        user = User.objects.filter(phone_number=phone_number, user_type=UserTypes.COURIER).first()
        if user:
            # Update existing courier user
            if password:
                user.set_password(password)
            if chat_id:
                user.chat_id = chat_id
            user.save()
        else:
            # Create new user
            if not password:
                raise serializers.ValidationError("Password is required for new users")

            # Create user with username set to phone number to ensure it's unique and consistent
            username = phone_number.replace('+', '')  # Remove + for username
            user = User.objects.create_user(
                username=username,
                phone_number=phone_number,
                password=password,
                user_type=UserTypes.COURIER,
                chat_id=chat_id
            )

        # Create courier instance
        courier = Courier.objects.create(user=user, **validated_data)
        # Assign vendors many-to-many field properly
        if vendors:
            courier.vendors.set(vendors)
        return courier

    @transaction.atomic
    def update(self, instance, validated_data):
        """
        Update courier and associated user in a transaction
        """
        vendors = validated_data.pop('vendors', None)
        user_data = validated_data.pop('user', {})
        phone_number = user_data.get('phone_number')
        chat_id = user_data.get('chat_id')
        password = validated_data.pop('password', None)

        # Update associated user
        if instance.user:
            user = instance.user
            if phone_number and user.phone_number != phone_number:
                # Check if new phone number is already used by another courier
                existing_user = User.objects.filter(
                    phone_number=phone_number,
                    user_type=UserTypes.COURIER
                ).exclude(id=user.id).first()
                if existing_user:
                    raise serializers.ValidationError(
                        "Phone number already registered as Courier"
                    )
                user.phone_number = phone_number

            if chat_id:
                user.chat_id = chat_id

            if password:
                user.set_password(password)

            user.save()

        # Update courier instance
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        instance.save()

        # Assign vendors many-to-many field properly
        if vendors is not None:
            instance.vendors.set(vendors)

        return instance


class CourierVendorSerializer(serializers.ModelSerializer):
    """
    Serializer for vendors assigned to a courier
    """
    phone = serializers.CharField(source='user.phone_number', read_only=True)
    address = serializers.SerializerMethodField()

    class Meta:
        model = Vendor
        fields = [
            'id',
            'name',
            'logo',
            'phone',
            'address'
        ]

    def get_address(self, obj):
        """
        Get address from region and district
        """
        address_parts = []
        if obj.region:
            address_parts.append(obj.region.name)
        if obj.district:
            address_parts.append(obj.district.name)
        return ", ".join(address_parts) if address_parts else None
